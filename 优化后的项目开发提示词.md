# 宠物博客站群系统开发提示词（优化版）

## 🎯 项目概述与核心目标

我需要开发一个专业的宠物博客站群系统，专注于猫狗知识分享，面向全球多个国家市场。这是一个商业级项目，要求在Google搜索中获得优秀排名，支持多语言、多域名独立运营。

### 核心商业目标
- 在Google搜索中获得优秀的SEO排名
- 支持多国家/多语言独立站点运营
- 实现高质量内容的批量生产和分发
- 建立可扩展的站群管理体系

### 目标市场
- 主要市场：美国（英语）、德国（德语）、俄罗斯（俄语）
- 预期访问量：前期1万/月，后期可扩展
- 扩展性：支持后续添加更多国家和语言

## 🏗️ 技术架构要求

### 前端技术栈
- **框架**：Astro（静态站点生成，SEO友好）
- **样式**：Tailwind CSS（响应式设计）
- **构建**：Vite（开发和构建工具）
- **部署**：静态文件部署

### 后端技术栈
- **请你选择最适合的后端技术栈**，要求：
  - 支持RESTful API
  - 高性能和可扩展性
  - 易于部署和维护
  - 支持文件上传和处理
  - 良好的ORM支持

### 数据库
- **类型**：MySQL 5.7.44
- **连接信息**：
  - IP：************
  - 数据库名：bengtai
  - 账号：bengtai
  - 密码：weizhen258

### 部署环境
- **服务器**：Linux VPS + 宝塔面板
- **开发环境**：Mac本地开发
- **域名策略**：每个语言使用独立顶级域名

## 📋 详细功能需求

### 1. 前端页面系统

#### 1.1 页面类型清单
请在文档中明确列出需要开发的所有页面类型：
- 首页（Homepage）
- 文章详情页（Article Detail）
- 分类页面（Category Pages）
- 搜索结果页（Search Results）
- 关于我们页（About Us）
- 隐私政策页（Privacy Policy）
- 联系我们页（Contact Us）
- 404错误页（404 Error Page）
- 网站地图页（Sitemap）

#### 1.2 页面设计要求
- **设计风格**：大气美观，专业的宠物博客风格
- **响应式**：完美适配桌面、平板、手机
- **加载速度**：首屏加载时间 < 3秒
- **用户体验**：简洁直观的导航和布局

#### 1.3 前后端交互接口
请在文档中详细说明每个页面需要调用的API接口：
- 首页：获取最新文章、热门文章、分类列表
- 文章页：获取文章详情、相关文章、评论列表
- 分类页：获取分类文章列表、分页
- 搜索页：搜索接口、搜索建议
- 评论系统：提交评论、获取评论

### 2. 多语言多域名策略

#### 2.1 模板架构
- **一语言一模板**：每种语言都是完全独立的前端模板
- **不使用i18n**：避免运行时语言切换，提升SEO效果
- **模板结构**：
  ```
  templates/
  ├── en-us/     # 英语模板
  ├── de-de/     # 德语模板
  ├── ru-ru/     # 俄语模板
  └── zh-cn/     # 中文模板（管理用）
  ```

#### 2.2 域名绑定机制
- **域名识别**：通过HTTP Host头自动识别域名
- **语言映射**：后台配置域名与语言模板的对应关系
- **路由处理**：根据域名自动加载对应语言的模板和内容

#### 2.3 扩展性设计
- **新语言添加流程**：复制现有模板 → 翻译内容 → 后台配置 → 域名绑定
- **模板管理**：支持模板的增删改查
- **配置管理**：支持语言配置的动态修改

### 3. 内容管理系统

#### 3.1 文章编辑器
- **富文本编辑器**：支持图文混排
- **本地粘贴**：支持从本地直接粘贴文字和图片
- **图片处理**：自动压缩、格式转换、CDN分发
- **预览功能**：实时预览文章效果

#### 3.2 翻译工作流
- **原始文章**：中文撰写和编辑
- **AI翻译**：集成OpenAI API进行自动翻译
- **翻译配置**：
  - API地址：https://ai.wanderintree.top
  - 密钥：sk-SMXjycC5GnJRswpJB8Ef6f632d794bBa9a1bAbB828E7Ee9d
  - 模型：gemini-2.5-pro
- **工作流程**：中文原文 → AI翻译 → 草稿保存 → 人工校对 → 发布

#### 3.3 内容分发
- **多语言发布**：一键发布到多个语言站点
- **发布状态**：草稿、已发布、已下线
- **定时发布**：支持定时发布功能

### 4. 评论系统

#### 4.1 评论功能
- **多层嵌套**：支持评论回复的多层嵌套
- **用户信息**：只需要用户名和邮箱，无需注册
- **审核机制**：所有评论必须后台审核后才能显示
- **反垃圾**：基本的垃圾评论过滤

#### 4.2 评论管理
- **审核界面**：批量审核、单个审核
- **评论状态**：待审核、已通过、已拒绝
- **评论统计**：评论数量、审核状态统计

### 5. SEO优化要求

#### 5.1 技术SEO
- **URL结构**：使用本地化语言的URL Slug
  - 英语：/pets/dog-training-tips
  - 德语：/haustiere/hundetraining-tipps
  - 俄语：/питомцы/советы-по-дрессировке-собак
- **元数据**：每篇文章独立设置标题、描述、关键词
- **结构化数据**：Schema.org标记（Article、BreadcrumbList等）
- **网站地图**：自动生成XML sitemap
- **页面速度**：Core Web Vitals优化

#### 5.2 内容SEO
- **分类体系**：
  - 一级分类：猫咪知识、狗狗知识、宠物健康、宠物用品
  - 二级分类：品种介绍、训练技巧、疾病预防、营养指南
- **内链策略**：相关文章推荐、分类导航
- **图片SEO**：Alt标签、文件名优化、图片压缩

### 6. 广告和统计系统

#### 6.1 广告管理
- **广告类型**：Google AdSense
- **广告位置**：文章内容中、侧边栏、页面底部（不影响用户体验）
- **独立配置**：每个语言站点独立的广告代码和开关
- **开关控制**：关闭时完全不显示广告代码和占位符

#### 6.2 统计分析
- **Google Analytics**：每个站点独立的GA代码
- **统计配置**：后台统一管理各站点的统计代码
- **数据隔离**：不同站点的数据完全独立

## 🛠️ 开发环境配置

### 本地开发环境
- **操作系统**：macOS
- **开发工具**：VS Code + 相关插件
- **数据库**：连接远程MySQL数据库
- **域名测试**：使用hosts文件模拟多域名访问

### 测试环境配置
- **多域名测试**：本地配置多个测试域名
- **数据库测试**：使用测试数据库或数据隔离
- **API测试**：完整的API接口测试

## 📚 文档输出要求

### 必须生成的文档
1. **项目总体架构文档**（Architecture.md）
2. **详细开发计划文档**（Development-Plan.md）
3. **API接口设计文档**（API-Design.md）
4. **数据库设计文档**（Database-Design.md）
5. **前端页面设计文档**（Frontend-Design.md）
6. **SEO优化指南文档**（SEO-Guide.md）
7. **多语言实现方案文档**（Multi-Language-Solution.md）
8. **部署和运维文档**（Deployment-Guide.md）
9. **测试计划文档**（Testing-Plan.md）
10. **开发步骤详细清单**（Development-Steps.md）

### 文档质量要求
- **详细程度**：每个文档都要详细到可以直接指导AI开发
- **结构清晰**：使用标准的Markdown格式，层次分明
- **可执行性**：每个步骤都要具体可执行，避免模糊描述
- **完整性**：覆盖项目的所有方面，不遗漏重要内容

## 🔄 开发流程要求

### 开发步骤拆分原则
- **小步骤**：每个步骤的工作量控制在Claude 200K上下文范围内
- **独立性**：每个步骤都要相对独立，可以单独完成
- **可测试**：每个步骤完成后都要有对应的测试验证
- **渐进式**：从基础功能到高级功能，逐步构建

### 开发阶段划分
1. **项目初始化阶段**：项目结构、基础配置
2. **数据库设计阶段**：表结构设计、关系建立
3. **后端API开发阶段**：核心接口开发
4. **前端模板开发阶段**：页面模板制作
5. **功能集成阶段**：前后端联调
6. **SEO优化阶段**：SEO功能实现
7. **测试验证阶段**：功能测试、性能测试
8. **部署上线阶段**：生产环境部署

### 测试要求
- **单元测试**：核心功能的单元测试
- **集成测试**：API接口的集成测试
- **端到端测试**：完整业务流程测试
- **性能测试**：页面加载速度、数据库性能
- **SEO测试**：SEO效果验证

## ✅ 成功标准和验收条件

### 功能验收标准
- 所有页面正常显示和交互
- 多语言多域名正确切换
- 文章发布和翻译流程完整
- 评论系统正常工作
- 广告和统计代码正确嵌入
- SEO元素完整实现

### 性能验收标准
- 首页加载时间 < 3秒
- 文章页加载时间 < 2秒
- Google PageSpeed Insights 评分 > 90
- Core Web Vitals 指标达标

### SEO验收标准
- 所有页面都有完整的meta标签
- URL结构符合SEO最佳实践
- 结构化数据正确实现
- 网站地图自动生成
- 图片SEO优化完成

## 🚨 重要提醒

1. **不要编写代码**：现阶段只需要生成详细的开发文档
2. **技术选型**：后端技术栈请你根据最佳实践自行选择
3. **文档质量**：文档质量直接影响后续AI开发的成功率
4. **细节完整**：所有技术细节都要在文档中明确说明
5. **可执行性**：确保每个开发步骤都具体可执行

请基于以上需求，生成完整、详细、可执行的项目开发文档体系。
